import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../config/theme/app_theme.dart';
import '../../models/appointment.dart';
import '../../providers/calendar_provider.dart';
import '../../services/staff_service.dart';
import '../../models/user_role.dart';
import '../dialogs/block_time_dialog.dart';
import 'block_time_block.dart';
import 'draggable_appointment_block.dart';
import 'droppable_time_slot.dart';
import 'time_slot.dart';



class DayView extends StatefulWidget {
  /// Global key to access state for scrolling from outside the widget tree
  static final GlobalKey<_DayViewState> globalKey = GlobalKey<_DayViewState>();
  final DateTime selectedDate;
  final List<Appointment> appointments;
  final Function(Appointment) onAppointmentTap;
  final Function(Map<String, dynamic>)? onBlockTap;
  final Function(DateTime, String?)? onTimeSlotTap; // Added staff ID parameter
  final Map<String, dynamic> businessHours;

  const DayView({
    Key? key,
    required this.selectedDate,
    required this.appointments,
    required this.onAppointmentTap,
    this.onBlockTap,
    this.onTimeSlotTap,
    required this.businessHours,
  }) : super(key: key);

  @override
  State<DayView> createState() => _DayViewState();
}

class _DayViewState extends State<DayView> {
  late ScrollController _horizontalScrollController;
  late ScrollController _staffHeaderScrollController;
  late ScrollController _verticalScrollController;
  late ScrollController _timeScrollController;



  @override
  void initState() {
    super.initState();

    _horizontalScrollController = ScrollController();
    _staffHeaderScrollController = ScrollController();
    _verticalScrollController = ScrollController();
    _timeScrollController = ScrollController();

    // Sync time scroll with main content scroll
    _verticalScrollController.addListener(() {
      if (_timeScrollController.hasClients) {
        _timeScrollController.jumpTo(_verticalScrollController.offset);
      }
    });

    // Sync horizontal scrolling between staff header and appointment content
    _horizontalScrollController.addListener(() {
      if (_staffHeaderScrollController.hasClients &&
          _staffHeaderScrollController.offset != _horizontalScrollController.offset) {
        _staffHeaderScrollController.jumpTo(_horizontalScrollController.offset);
      }
    });

    _staffHeaderScrollController.addListener(() {
      if (_horizontalScrollController.hasClients &&
          _horizontalScrollController.offset != _staffHeaderScrollController.offset) {
        _horizontalScrollController.jumpTo(_staffHeaderScrollController.offset);
      }
    });
  }

  @override
  void didUpdateWidget(DayView oldWidget) {
    super.didUpdateWidget(oldWidget);
    // No special handling needed since we're not using PageView anymore
  }

  @override
  void dispose() {
    _horizontalScrollController.dispose();
    _staffHeaderScrollController.dispose();
    _verticalScrollController.dispose();
    _timeScrollController.dispose();
    super.dispose();
  }

  /// Scroll to the current time
  void scrollToCurrentTime() {
    scrollToTime(DateTime.now());
  }

  /// Navigate to today and scroll to current time
  void goToToday() {
    scrollToCurrentTime();
  }



  @override
  Widget build(BuildContext context) {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        final currentDisplayDate = widget.selectedDate;

        // Determine time range based on hour view mode
        final businessOpenTime = widget.businessHours['openTime'] as int;
        final businessCloseTime = widget.businessHours['closeTime'] as int;
        final openTime = provider.showFullDay ? 0 : businessOpenTime;
        final closeTime = provider.showFullDay ? 24 : businessCloseTime;
        final totalHours = closeTime - openTime;
        final slotHeight = provider.getResponsiveTimeSlotHeight(context);

        // Calculate staff layout - show all selected staff (no 3-groomer limit)
        final staff = provider.availableStaff;
        final selectedStaffIds = provider.selectedStaff;
        final allVisibleStaff = staff.where((s) => selectedStaffIds.contains(s.id)).toList();

        final screenWidth = MediaQuery.of(context).size.width;
        const timeColumnWidth = 60.0;
        final availableWidth = screenWidth - timeColumnWidth;

        // Calculate column width with proper constraints to prevent overflow
        // Use responsive minimum width based on screen size
        final minColumnWidth = provider.getResponsiveStaffColumnWidth(context);
        double staffColumnWidth;
        double totalCalendarWidth;

        if (allVisibleStaff.isEmpty) {
          staffColumnWidth = minColumnWidth;
          totalCalendarWidth = staffColumnWidth;
        } else {
          // For day view, maintain the visual layout by calculating width as if max 3 staff
          // but allow horizontal scrolling for more staff
          final visibleStaffCount = allVisibleStaff.length.clamp(1, 3);
          final safeAvailableWidth = availableWidth - 8.0; // Reserve 8px total for safety
          final distributedWidth = safeAvailableWidth / visibleStaffCount;

          if (distributedWidth >= minColumnWidth) {
            staffColumnWidth = distributedWidth.floorToDouble(); // Use floor to prevent fractional pixels
          } else {
            staffColumnWidth = minColumnWidth;
          }

          // Total calendar width includes all staff members for horizontal scrolling
          totalCalendarWidth = staffColumnWidth * allVisibleStaff.length;
        }

        return Container(
          color: Theme.of(context).colorScheme.surface, // Use theme-aware background
          child: Column(
            children: [
              Expanded(
                child: GestureDetector(
                  onScaleUpdate: (details) {
                    // Pinch-to-zoom functionality
                    if (details.scale != 1.0) {
                      final currentHeight = provider.timeSlotHeight;
                      final newHeight = (currentHeight * details.scale).clamp(
                        provider.minTimeSlotHeight,
                        provider.maxTimeSlotHeight,
                      );
                      if ((newHeight - currentHeight).abs() > 2) {
                        provider.setTimeSlotHeight(newHeight);
                      }
                    }
                  },
                  child: Row(
                    children: [
                      // Fixed time labels column
                      _buildStaticTimeColumn(openTime, closeTime, totalHours, slotHeight, currentDisplayDate),
                      // Scrollable content area
                      Expanded(
                        child: Column(
                          children: [
                            // Day header showing current date
                            _buildDayHeader(currentDisplayDate),
                            // Staff header with horizontal scrolling
                            _buildScrollableStaffHeader(allVisibleStaff, staffColumnWidth, totalCalendarWidth),
                            // Appointment content with horizontal scrolling
                            Expanded(
                              child: _buildScrollableAppointmentContent(
                                provider, allVisibleStaff, staffColumnWidth, slotHeight,
                                totalHours, openTime, closeTime, totalCalendarWidth, currentDisplayDate
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Static time column that doesn't change during within-week navigation
  Widget _buildStaticTimeColumn(int openTime, int closeTime, int totalHours, double slotHeight, DateTime currentDate) {
    return Container(
      width: 60,
      color: Theme.of(context).colorScheme.surface,
      child: Column(
        children: [
          _buildTimeHeader(),
          Expanded(
            child: SingleChildScrollView(
              controller: _timeScrollController,
              physics: const NeverScrollableScrollPhysics(),
              child: Column(
                children: List.generate(totalHours, (index) {
                  final hour = openTime + index;
                  final time = DateTime(
                    currentDate.year,
                    currentDate.month,
                    currentDate.day,
                    hour,
                  );
                  final isCurrentHour = DateTime.now().hour == hour &&
                      DateTime.now().day == currentDate.day &&
                      DateTime.now().month == currentDate.month &&
                      DateTime.now().year == currentDate.year;

                  return TimeLabel(
                    time: time,
                    isCurrentHour: isCurrentHour,
                    height: slotHeight,
                  );
                }),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Day header showing the current selected date
  Widget _buildDayHeader(DateTime selectedDate) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final isToday = _isToday(selectedDate);

    return Container(
      height: 48,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: isDark ? AppColors.darkSurfaceVariant : AppColors.lightGray,
            width: 0.5,
          ),
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              DateFormat('EEEE', 'ro').format(selectedDate),
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurface,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
              decoration: BoxDecoration(
                color: isToday
                    ? Theme.of(context).colorScheme.primary
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                DateFormat('d MMMM yyyy', 'ro').format(selectedDate),
                style: TextStyle(
                  color: isToday
                      ? Theme.of(context).colorScheme.onPrimary
                      : Theme.of(context).colorScheme.onSurface,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Scrollable staff header with horizontal scrolling support
  Widget _buildScrollableStaffHeader(List<StaffResponse> staff, double columnWidth, double totalWidth) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      height: 32,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: isDark ? AppColors.darkSurfaceVariant : AppColors.lightGray,
            width: 0.5,
          ),
        ),
      ),
      child: SingleChildScrollView(
        controller: _staffHeaderScrollController,
        scrollDirection: Axis.horizontal,
        physics: const ClampingScrollPhysics(),
        child: SizedBox(
          width: totalWidth,
          child: Row(
            children: staff
                .map((staffMember) => SizedBox(
                      width: columnWidth,
                      child: Container(
                        height: 32,
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surface,
                          border: Border(
                            bottom: BorderSide(
                              color: AppTheme.getStaffColor(staffMember.id),
                              width: 3,
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            // Staff name
                            Expanded(
                              child: Center(
                                child: Text(
                                  _getStaffDisplayName(staffMember),
                                  style: TextStyle(
                                    color: AppTheme.getStaffColor(staffMember.id),
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  textAlign: TextAlign.center,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ),
                            // Feature 2: Add slot button with improved UI
                            if (_canAddSlotForStaff(staffMember))
                              Tooltip(
                                message: 'Slot nou',
                                child: GestureDetector(
                                  onTap: () => _addSlotForStaff(staffMember),
                                  child: Container(
                                    width: 18,
                                    height: 18,
                                    margin: const EdgeInsets.only(left: 2, right: 2),
                                    decoration: BoxDecoration(
                                      color: AppTheme.getStaffColor(staffMember.id).withValues(alpha: 0.3),
                                      borderRadius: BorderRadius.circular(9),
                                      border: Border.all(
                                        color: AppTheme.getStaffColor(staffMember.id),
                                        width: 1,
                                      ),
                                    ),
                                    child: Icon(
                                      Icons.add,
                                      size: 10,
                                      color: AppTheme.getStaffColor(staffMember.id),
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ))
                .toList(),
          ),
        ),
      ),
    );
  }

  // Scrollable appointment content with horizontal scrolling support
  Widget _buildScrollableAppointmentContent(
    CalendarProvider provider,
    List<StaffResponse> allVisibleStaff,
    double staffColumnWidth,
    double slotHeight,
    int totalHours,
    int openTime,
    int closeTime,
    double totalCalendarWidth,
    DateTime selectedDate,
  ) {
    final lunchStart = widget.businessHours['lunchBreak']['start'] as int;
    final lunchEnd = widget.businessHours['lunchBreak']['end'] as int;
    final isWorkDay = provider.isWorkingDay(selectedDate);

    return SingleChildScrollView(
      controller: _horizontalScrollController,
      scrollDirection: Axis.horizontal,
      physics: const ClampingScrollPhysics(),
      child: SizedBox(
        width: totalCalendarWidth,
        child: SingleChildScrollView(
          controller: _verticalScrollController,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: allVisibleStaff
                .map((staffMember) => SizedBox(
                      width: staffColumnWidth,
                      child: _buildStaffColumn(
                        provider,
                        staffMember,
                        staffColumnWidth,
                        slotHeight,
                        totalHours,
                        openTime,
                        closeTime,
                        lunchStart,
                        lunchEnd,
                        isWorkDay,
                        selectedDate,
                      ),
                    ))
                .toList(),
          ),
        ),
      ),
    );
  }





  Widget _buildTimeHeader() {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      width: 60,
      height: 48 + 32, // Match day header (48) + staff header (32) = 80 total
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          right: BorderSide(
            color: isDark ? AppColors.darkSurfaceVariant : AppColors.lightGray,
            width: 0.5,
          ),
        ),
      ),
      child: const SizedBox.shrink(), // No text, just spacing
    );
  }

  // Feature 2: Helper methods for slot management
  String _getStaffDisplayName(StaffResponse staffMember) {
    // Check if this is a slot (additional staff member linked to original groomer)
    if (staffMember.notes?.contains('SLOT_FOR:') == true) {
      final originalStaffId = staffMember.notes!.split('SLOT_FOR:')[1].split('|')[0];
      final slotNumber = _getSlotNumber(staffMember.notes!);
      final originalStaff = context.read<CalendarProvider>().getStaffById(originalStaffId);
      return '${originalStaff?.displayName ?? 'Staff'} - Slot $slotNumber';
    }
    return staffMember.displayName;
  }

  int _getSlotNumber(String notes) {
    final match = RegExp(r'SLOT_NUMBER:(\d+)').firstMatch(notes);
    return match != null ? int.parse(match.group(1)!) : 1;
  }

  bool _canAddSlotForStaff(StaffResponse staffMember) {
    // Don't show + button for slots themselves
    if (staffMember.notes?.contains('SLOT_FOR:') == true) {
      return false;
    }

    // Check subscription limits
    final currentSlotCount = _getCurrentSlotCount(staffMember.id);
    // For now, allow up to 3 additional slots per groomer
    return currentSlotCount < 3;
  }

  int _getCurrentSlotCount(String originalStaffId) {
    final provider = context.read<CalendarProvider>();
    return provider.availableStaff
        .where((staff) => staff.notes?.contains('SLOT_FOR:$originalStaffId') == true)
        .length;
  }

  Future<void> _addSlotForStaff(StaffResponse originalStaff) async {
    try {
      final provider = context.read<CalendarProvider>();
      final currentSlotCount = _getCurrentSlotCount(originalStaff.id);
      final newSlotNumber = currentSlotCount + 2; // Start from 2 (original is slot 1)

      // Create a new staff member as a slot
      final slotNotes = 'SLOT_FOR:${originalStaff.id}|SLOT_NUMBER:$newSlotNumber';
      final slotNickname = '${originalStaff.displayName} - Slot $newSlotNumber';

      // Call the staff creation service
      await _createSlotStaff(slotNickname, originalStaff.groomerRole.value, slotNotes);

      // Refresh calendar data
      await provider.refreshStaffData();

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Slot adăugat pentru ${originalStaff.displayName}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Eroare la adăugarea slot-ului: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _createSlotStaff(String nickname, String role, String notes) async {
    // Convert role string to GroomerRole enum
    GroomerRole groomerRole;
    switch (role.toLowerCase()) {
      case 'chief_groomer':
        groomerRole = GroomerRole.chiefGroomer;
        break;
      case 'assistant':
        groomerRole = GroomerRole.assistant;
        break;
      default:
        groomerRole = GroomerRole.groomer;
    }

    // Create the request
    final request = CreateStaffDirectlyRequest.fromInput(
      nickname: nickname,
      groomerRole: groomerRole,
      notes: notes,
    );

    // Call the staff service
    final response = await StaffService.createStaffDirectlyInCurrentSalon(request);

    if (!response.success) {
      throw Exception(response.error ?? 'Failed to create slot staff');
    }
  }

  void _showQuickActionMenu(DateTime slotTime, String staffId) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading:  Icon(Icons.add),
              title: Text('Programare nouă'),
              onTap: () {
                Navigator.pop(context);
                widget.onTimeSlotTap?.call(slotTime, staffId);
              },
            ),
            ListTile(
              leading:  Icon(Icons.block),
              title: Text('Blochează timp'),
              onTap: () {
                Navigator.pop(context);
                showDialog(
                  context: context,
                  builder: (_) => BlockTimeDialog(
                    selectedDate: slotTime,
                    preselectedStaffId: staffId,
                    preselectedStartTime: TimeOfDay.fromDateTime(slotTime),
                  ),
                );
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildStaffColumn(
    CalendarProvider provider,
    StaffResponse staffMember,
    double columnWidth,
    double slotHeight,
    int totalHours,
    int openTime,
    int closeTime,
    int lunchStart,
    int lunchEnd,
    bool isWorkDay,
    DateTime selectedDate,
  ) {
    // Get staff-specific working hours for this specific day
    final staffSettings = provider.getStaffWorkingHoursSettings(staffMember.id);
    int staffStartHour = openTime;
    int staffEndHour = closeTime;

    if (staffSettings != null) {
      final dayOfWeek = _getDayOfWeekString(selectedDate);
      final daySchedule = staffSettings.getScheduleForDay(dayOfWeek);

      if (daySchedule != null && daySchedule.isWorkingDay &&
          daySchedule.startTime != null && daySchedule.endTime != null) {
        // Parse staff start and end hours
        final startHour = int.tryParse(daySchedule.startTime!.split(':')[0]) ?? openTime;
        final endHour = int.tryParse(daySchedule.endTime!.split(':')[0]) ?? closeTime;

        // Use staff hours directly
        staffStartHour = startHour;
        staffEndHour = endHour;
      }
    }

    final staffAppointments = widget.appointments.where((appointment) {
      // First filter by date - only show appointments for the selected date
      final appointmentDate = DateTime(
        appointment.startTime.year,
        appointment.startTime.month,
        appointment.startTime.day,
      );
      final targetDate = DateTime(
        selectedDate.year,
        selectedDate.month,
        selectedDate.day,
      );

      if (!appointmentDate.isAtSameMomentAs(targetDate)) {
        return false;
      }

      // Then filter by staff member
      if (appointment.groomerId != null &&
          appointment.groomerId!.isNotEmpty) {
        return appointment.groomerId == staffMember.id;
      }

      String appointmentStaffName;
      try {
        appointmentStaffName =
            appointment.assignedGroomer?.isNotEmpty == true
                ? appointment.assignedGroomer!
                : 'Ana Popescu';
      } catch (e) {
        appointmentStaffName = 'Ana Popescu';
      }

      return appointmentStaffName == staffMember.name ||
          appointmentStaffName == staffMember.displayName;
    }).toList();

    // Debug logging to help troubleshoot
    if (staffAppointments.isNotEmpty) {
    }

    final dayBlocks = provider.getBlockedTimesForDate(selectedDate);
    final staffBlocks = dayBlocks.where((block) {
      final ids = (block['staffIds'] as List).cast<String>();
      return ids.contains(staffMember.id);
    }).toList();

    return Stack(
      clipBehavior: Clip.hardEdge, // Ensure stack contents are clipped
      children: [
          Column(
            children: List.generate(totalHours, (index) {
              final hour = openTime + index;
              final slotTime = DateTime(
                selectedDate.year,
                selectedDate.month,
                selectedDate.day,
                hour,
              );

              final isPastSlot = slotTime.isBefore(DateTime.now());

              // Check if this hour is within staff working hours
              final isWithinStaffHours = hour >= staffStartHour && hour < staffEndHour;
              
              // Get comprehensive styling information
              final slotStyling = provider.getTimeSlotStyling(slotTime, staffMember.id);

              // Determine if this is a business hour (considering salon closure and staff availability)
              final isBusinessHour = provider.showFullDay
                  ? (slotStyling.isAvailable && isWithinStaffHours)
                  : (slotStyling.isAvailable && isWithinStaffHours);

              final isLunchBreak = hour >= lunchStart && hour < lunchEnd;
              final hasAppointment = staffAppointments.any((apt) =>
                  apt.startTime.hour <= hour && apt.endTime.hour > hour);

              // If outside staff hours, show greyed out slot
              if (!isWithinStaffHours) {
                return DroppableTimeSlot(
                  dateTime: slotTime,
                  staffId: staffMember.id,
                  isBusinessHour: false,
                  isLunchBreak: false,
                  isAvailable: false,
                  isGreyedOut: true,
                  isPastSlot: isPastSlot,
                  height: slotHeight,
                  isDragEnabled: true,
                );
              }

              return Container(
                decoration: BoxDecoration(
                  // Remove extra borders - let TimeSlot handle its own borders
                  color: slotStyling.isGreyedOut
                      ? Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.1)
                      : null,
                ),
                child: DroppableTimeSlot(
                  dateTime: slotTime,
                  staffId: staffMember.id,
                  isBusinessHour: isBusinessHour && !slotStyling.isGreyedOut,
                  isLunchBreak: isLunchBreak,
                  isAvailable: !hasAppointment && slotStyling.isAvailable,
                  isGreyedOut: slotStyling.isGreyedOut,
                  isPastSlot: isPastSlot,
                  height: slotHeight,
                  onTap: slotStyling.isInteractive
                      ? () => widget.onTimeSlotTap?.call(slotTime, staffMember.id)
                      : null,
                  onLongPress: slotStyling.isInteractive
                      ? () => _showQuickActionMenu(slotTime, staffMember.id)
                      : null,
                  isDragEnabled: true,
                  // Pass disabled reason to TimeSlot for internal handling
                  child: slotStyling.disabledReason != null
                      ? Container(
                          decoration: BoxDecoration(
                            color: Colors.transparent,
                          ),
                        )
                      : null,
                ),
              );
            }),
          ),
          ...staffBlocks.map((block) {
                final start = DateTime.parse(block['startTime']).toLocal();
                final end = DateTime.parse(block['endTime']).toLocal();

                final topOffset = ((start.hour - openTime) * slotHeight) +
                    (start.minute / 60 * slotHeight);
                final duration = end.difference(start);
                final blockHeight = (duration.inMinutes / 60) * slotHeight;

                return Positioned(
                  top: topOffset,
                  left: 2,
                  right: 2,
                  child: BlockTimeBlock(
                    block: block,
                    height: blockHeight,
                    onTap: () => widget.onBlockTap?.call(block),
                  ),
                );
          }),
          ...staffAppointments.map((appointment) {
                final startHour = appointment.startTime.hour;
                final startMinute = appointment.startTime.minute;

                final topOffset = ((startHour - openTime) * slotHeight) +
                    (startMinute / 60 * slotHeight);
                final duration =
                    appointment.endTime.difference(appointment.startTime);
                final blockHeight = (duration.inMinutes / 60) * slotHeight;

                return Positioned(
                  top: topOffset,
                  left: 0,
                  right: 0,
                  child: DraggableAppointmentBlock(
                    appointment: appointment,
                    height: blockHeight,
                    onTap: () => widget.onAppointmentTap(appointment),
                    isCompact: false,
                    isDragEnabled: true,
                  ),
                );
          }),
          if (_isToday(selectedDate))
            _buildCurrentTimeIndicator(openTime, slotHeight),
        ],
    );
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  Widget _buildCurrentTimeIndicator(int openTime, double slotHeight) {
    final now = DateTime.now();
    final currentHour = now.hour;
    final currentMinute = now.minute;

    // In full day mode, show indicator for any hour; in business mode, only during business hours
    final businessCloseTime = widget.businessHours['closeTime'] as int;
    final showFullDay = context.read<CalendarProvider>().showFullDay;

    if (!showFullDay && (currentHour < openTime || currentHour >= businessCloseTime)) {
      return SizedBox.shrink();
    }

    if (showFullDay && (currentHour < openTime || currentHour >= 24)) {
      return SizedBox.shrink();
    }

    final topOffset = ((currentHour - openTime) * slotHeight) +
        (currentMinute / 60 * slotHeight);

    return Positioned(
      top: topOffset,
      left: 0,
      right: 0,
      child: Container(
        height: 2,
        color: Colors.red,
        child: Row(
          children: [
            Container(
              width: 6,
              height: 6,
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
            ),
            Expanded(
              child: Container(
                height: 2,
                color: Colors.red,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  // Helper method to get day of week string
  String _getDayOfWeekString(DateTime date) {
    const dayNames = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    return dayNames[date.weekday - 1];
  }

  /// Scroll vertically to show the specified time slot
  void scrollToTime(DateTime time) {
    final provider = context.read<CalendarProvider>();
    final openTime = provider.showFullDay
        ? 0
        : widget.businessHours['openTime'] as int;
    final slotHeight = provider.getResponsiveTimeSlotHeight(context);

    final offset = ((time.hour - openTime) * slotHeight) +
        (time.minute / 60 * slotHeight);

    if (_verticalScrollController.hasClients) {
      final max = _verticalScrollController.position.maxScrollExtent;
      _verticalScrollController.animateTo(
        offset.clamp(0.0, max),
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }

    if (_timeScrollController.hasClients) {
      final max = _timeScrollController.position.maxScrollExtent;
      _timeScrollController.animateTo(
        offset.clamp(0.0, max),
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// Convenience method to scroll to the current time
  // void scrollToCurrentTime() {
  //   scrollToTime(DateTime.now());
  // }
}
